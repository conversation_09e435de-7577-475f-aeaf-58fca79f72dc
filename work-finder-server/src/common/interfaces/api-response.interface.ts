export interface ApiResponse<T = any> {
  data: T;
  message: string;
  status: 'success' | 'error';
  timestamp: string;
  meta?: {
    pagination?: PaginationMeta;
    sorting?: SortingMeta;
    filtering?: FilteringMeta;
    [key: string]: any;
  };
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  meta: {
    pagination: PaginationMeta;
    [key: string]: any;
  };
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface SortingMeta {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  availableFields?: string[];
}

export interface FilteringMeta {
  filters?: Record<string, any>;
  availableFields?: string[];
}

export interface ErrorResponse extends ApiResponse<null> {
  status: 'error';
  error: {
    code: string;
    details?: any;
    stack?: string; // Only in development
  };
}

// Response builder utility
export class ResponseBuilder {
  static success<T>(
    data: T,
    message: string = 'Operation successful',
    meta?: any
  ): ApiResponse<T> {
    return {
      data,
      message,
      status: 'success',
      timestamp: new Date().toISOString(),
      ...(meta && { meta }),
    };
  }

  static paginated<T>(
    data: T[],
    pagination: PaginationMeta,
    message: string = 'Data retrieved successfully'
  ): PaginatedResponse<T> {
    return {
      data,
      message,
      status: 'success',
      timestamp: new Date().toISOString(),
      meta: { pagination },
    };
  }

  static error(
    message: string,
    code: string = 'INTERNAL_ERROR',
    details?: any,
    statusCode: number = 500
  ): ErrorResponse {
    return {
      data: null,
      message,
      status: 'error',
      timestamp: new Date().toISOString(),
      error: {
        code,
        details,
        ...(process.env.NODE_ENV === 'development' && { stack: new Error().stack }),
      },
    };
  }
}
