import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  SetMetadata,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  ApiResponse,
  ResponseBuilder,
} from '../interfaces/api-response.interface';

// Decorator to skip response transformation
export const RAW_RESPONSE_KEY = 'raw_response';
export const RawResponse = () => SetMetadata(RAW_RESPONSE_KEY, true);

// Decorator to set custom response message
export const RESPONSE_MESSAGE_KEY = 'response_message';
export const ResponseMessage = (message: string) =>
  SetMetadata(RESPONSE_MESSAGE_KEY, message);

// Decorator to mark paginated responses
export const PAGINATED_RESPONSE_KEY = 'paginated_response';
export const PaginatedResponse = () =>
  SetMetadata(PAGINATED_RESPONSE_KEY, true);

@Injectable()
export class ResponseInterceptor implements NestInterceptor {
  constructor(private reflector: Reflector) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((data) => {
        // Skip transformation if @RawResponse() decorator is used
        const isRawResponse = this.reflector.getAllAndOverride<boolean>(
          RAW_RESPONSE_KEY,
          [context.getHandler(), context.getClass()],
        );

        if (isRawResponse) {
          return data;
        }

        // Check if data is already wrapped (to avoid double wrapping)
        if (this.isAlreadyWrapped(data)) {
          return data;
        }

        // Get custom message from decorator
        const customMessage = this.reflector.getAllAndOverride<string>(
          RESPONSE_MESSAGE_KEY,
          [context.getHandler(), context.getClass()],
        );

        // Check if this is a paginated response
        const isPaginated = this.reflector.getAllAndOverride<boolean>(
          PAGINATED_RESPONSE_KEY,
          [context.getHandler(), context.getClass()],
        );

        // Handle different response types
        if (isPaginated && this.isPaginatedData(data)) {
          return ResponseBuilder.paginated(
            data.data,
            data.pagination,
            customMessage || 'Data retrieved successfully',
          );
        }

        // Handle null/undefined responses (like DELETE operations)
        if (data === null || data === undefined) {
          return ResponseBuilder.success(
            null,
            customMessage || 'Operation completed successfully',
          );
        }

        // Handle array responses
        if (Array.isArray(data)) {
          return ResponseBuilder.success(
            data,
            customMessage || 'Data retrieved successfully',
          );
        }

        // Handle object responses
        return ResponseBuilder.success(
          data,
          customMessage || 'Operation successful',
        );
      }),
    );
  }

  private isAlreadyWrapped(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      'status' in data &&
      'data' in data &&
      'message' in data &&
      'timestamp' in data
    );
  }

  private isPaginatedData(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      'data' in data &&
      'pagination' in data &&
      Array.isArray(data.data)
    );
  }
}

// Utility function for manual response building in controllers
export function buildResponse<T>(
  data: T,
  message?: string,
  meta?: any,
): ApiResponse<T> {
  return ResponseBuilder.success(data, message, meta);
}

// Utility function for paginated responses
export function buildPaginatedResponse<T>(
  data: T[],
  page: number,
  limit: number,
  total: number,
  message?: string,
): ApiResponse<T[]> {
  const totalPages = Math.ceil(total / limit);

  return ResponseBuilder.paginated(
    data,
    {
      page,
      limit,
      total,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    },
    message,
  );
}
