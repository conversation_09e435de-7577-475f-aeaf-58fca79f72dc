version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: workfinder-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: workfinder_db
      POSTGRES_USER: workfinder_user
      POSTGRES_PASSWORD: workfinder_password
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - workfinder-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U workfinder_user -d workfinder_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis (for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: workfinder-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - workfinder-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # WorkFinder API
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: workfinder-api
    restart: unless-stopped
    environment:
      # Database
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: workfinder_user
      DB_PASSWORD: workfinder_password
      DB_NAME: workfinder_db
      
      # JWT
      JWT_SECRET: your_super_secret_jwt_key_for_development_make_it_long_and_secure
      JWT_EXPIRES_IN: 1h
      JWT_REFRESH_SECRET: your_refresh_secret_key_for_development_also_long_and_secure
      JWT_REFRESH_EXPIRES_IN: 7d
      
      # Application
      NODE_ENV: development
      PORT: 3000
      API_PREFIX: api/v1
      
      # File Upload
      UPLOAD_DEST: ./uploads
      MAX_FILE_SIZE: 10485760
      
      # Redis
      REDIS_HOST: redis
      REDIS_PORT: 6379
    ports:
      - "3000:3000"
    volumes:
      # Mount source code for hot reload
      - .:/app
      - /app/node_modules
      # Mount uploads directory
      - ./uploads:/app/uploads
    networks:
      - workfinder-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/v1/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # pgAdmin (Database Management UI)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: workfinder-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - workfinder-network
    depends_on:
      - postgres

  # Redis Commander (Redis Management UI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: workfinder-redis-ui
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - workfinder-network
    depends_on:
      - redis

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  workfinder-network:
    driver: bridge
