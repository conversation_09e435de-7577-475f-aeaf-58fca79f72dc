{"navigation": {"home": "Home", "jobs": "Jobs", "companies": "Companies", "about": "About", "contact": "Contact", "login": "<PERSON><PERSON>", "register": "Register", "dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "logout": "Logout", "findJobs": "Find Jobs", "employers": "Employers", "candidates": "Candidates", "blog": "Blog", "browseJobs": "Browse Jobs", "browseJobsDesc": "Search through all available positions", "browseCategories": "Browse Categories", "browseCategoriesDesc": "Explore jobs by industry and type", "jobAlerts": "<PERSON>", "jobAlertsDesc": "Get notified about new opportunities", "myBookmarks": "My Bookmarks", "myBookmarksDesc": "View your saved job listings", "browseCompanies": "Browse Companies", "browseCompaniesDesc": "Discover amazing workplaces", "employerDashboard": "Employer Dashboard", "employerDashboardDesc": "Manage your company profile", "addJob": "Add Job", "addJobDesc": "Post new job opportunities", "jobPackages": "Job Packages", "jobPackagesDesc": "Choose the right posting plan", "browseCandidates": "Browse Candidates", "browseCandidatesDesc": "Search through candidate profiles", "candidateDashboard": "Candidate Dashboard", "candidateDashboardDesc": "Manage your professional profile", "uploadCV": "Upload your CV", "loginRegister": "Login / Register", "jobPost": "Job Post"}, "common": {"loading": "Loading...", "error": "An error occurred", "success": "Success", "retry": "Retry", "cancel": "Cancel", "save": "Save", "edit": "Edit", "delete": "Delete", "confirm": "Confirm", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "clear": "Clear", "apply": "Apply", "submit": "Submit", "reset": "Reset"}, "homepage": {"title": "Find Your Dream Job", "subtitle": "Discover thousands of job opportunities and connect with top employers", "searchPlaceholder": "Search jobs, companies, or keywords", "searchButton": "Search Jobs", "featuredJobs": "Featured Jobs", "howItWorks": "How It Works", "popularCompanies": "Popular Companies", "latestBlog": "Latest Blog Posts", "getStarted": "Get Started Today"}, "jobs": {"title": "Job Opportunities", "searchResults": "Search Results", "noJobsFound": "No jobs found", "loadMore": "Load More", "filterBy": "Filter by", "sortBy": "Sort by", "location": "Location", "jobType": "Job Type", "experience": "Experience Level", "salary": "Salary Range", "company": "Company", "postedDate": "Posted Date", "applyNow": "Apply Now", "saveJob": "Save Job", "jobSaved": "Job saved successfully", "jobDetails": "Job Details", "requirements": "Requirements", "responsibilities": "Responsibilities", "benefits": "Benefits"}, "companies": {"title": "Companies", "searchCompanies": "Search Companies", "noCompaniesFound": "No companies found", "employees": "employees", "jobs": "jobs", "follow": "Follow", "following": "Following", "companyProfile": "Company Profile", "aboutCompany": "About Company", "openPositions": "Open Positions", "companyReviews": "Reviews"}, "auth": {"loginTitle": "Login to Your Account", "registerTitle": "Create New Account", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "forgotPassword": "Forgot Password?", "loginButton": "<PERSON><PERSON>", "registerButton": "Register", "orContinueWith": "Or continue with", "alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "signInWithGoogle": "Sign in with Google", "signInWithLinkedIn": "Sign in with LinkedIn", "rememberMe": "Remember me", "loginSuccess": "Login successful", "registerSuccess": "Account created successfully", "logoutSuccess": "Logged out successfully"}, "dashboard": {"welcome": "Welcome back", "overview": "Overview", "myApplications": "My Applications", "savedJobs": "Saved Jobs", "profile": "Profile", "resume": "Resume", "settings": "Settings", "notifications": "Notifications", "recentActivity": "Recent Activity", "jobAlerts": "<PERSON>", "applicationStatus": "Application Status", "pending": "Pending", "reviewed": "Reviewed", "interviewed": "Interviewed", "rejected": "Rejected", "accepted": "Accepted"}, "errors": {"generic": "Something went wrong. Please try again.", "network": "Network error. Please check your connection.", "unauthorized": "You are not authorized to access this resource.", "forbidden": "Access denied.", "notFound": "The requested resource was not found.", "validation": "Please check your input and try again.", "serverError": "Server error. Please try again later.", "sessionExpired": "Your session has expired. Please login again.", "emailRequired": "Email is required", "emailInvalid": "Please enter a valid email address", "emailNotVerified": "Please verify your email address", "passwordRequired": "Password is required", "passwordTooShort": "Password must be at least 8 characters", "passwordsDoNotMatch": "Passwords do not match", "firstNameRequired": "First name is required", "lastNameRequired": "Last name is required", "userAlreadyExists": "An account with this email already exists", "userNotFound": "User not found", "userInactive": "Your account is inactive. Please contact support.", "userSuspended": "Your account has been suspended. Please contact support.", "jobNotFound": "Job not found", "jobApplicationAlreadyExists": "You have already applied for this job", "jobApplicationDeadlinePassed": "The application deadline has passed", "jobInactive": "This job is no longer active", "companyNotFound": "Company not found", "companyInactive": "This company is no longer active", "fileTooLarge": "File size is too large. Maximum size is 10MB.", "fileTypeNotSupported": "File type not supported", "fileUploadFailed": "File upload failed. Please try again.", "serviceUnavailable": "Service is temporarily unavailable. Please try again later.", "rateLimitExceeded": "Too many requests. Please wait and try again.", "resourceConflict": "Resource conflict. Please refresh and try again.", "resourceGone": "This resource is no longer available"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Must be at least {min} characters", "maxLength": "Must be no more than {max} characters", "passwordStrength": "Password must contain at least one uppercase letter, one lowercase letter, and one number"}, "profile": {"title": "Profile Information", "changePhoto": "Change photo", "updateSuccess": "Profile updated successfully!", "updateError": "Failed to update profile. Please try again.", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "professionalInfo": "Professional Information", "socialLinks": "Social Links", "username": "Username", "fullName": "Full Name", "phone": "Phone", "address": "Address", "bio": "Bio", "website": "Website", "linkedIn": "LinkedIn", "github": "GitHub", "twitter": "Twitter", "location": "Location", "jobTitle": "Job Title", "company": "Company", "experience": {"label": "Experience Level", "entry": "Entry Level", "junior": "Junior", "mid": "Mid Level", "senior": "Senior", "lead": "Lead", "executive": "Executive"}, "availability": {"label": "Availability", "available": "Available", "not_available": "Not Available", "open_to_offers": "Open to Offers"}}, "hero": {"title": "There Are {count} Postings Here For You!", "subtitle": "Find Jobs, Employment & Career Opportunities. Some of the companies we've helped recruit excellent applicants over the years.", "searchPlaceholder": "Search jobs, companies, or keywords", "searchButton": "Search Jobs", "popular": "Popular:", "popularTags": {"designer": "Designer", "developer": "Developer", "web": "Web", "ios": "IOS", "php": "PHP", "senior": "Senior", "engineer": "Engineer"}, "stats": {"jobsAvailable": "10k+ Jobs Available", "updatedDaily": "Updated daily"}}, "emptyState": {"noResults": "No results found", "noResultsFor": "No results found for \"{searchTerm}\"", "tryDifferentKeywords": "Try different keywords or remove some filters", "suggestions": "Try searching for:", "clearSearch": "Clear search", "goBack": "Go back"}, "notFound": {"title": "Page Not Found", "description": "Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.", "searchSuggestion": "Try searching for what you need:", "popularPages": "Popular pages:", "browseJobs": "Browse Jobs", "companies": "Companies", "salaryGuide": "Salary Guide", "contactUs": "Contact Us", "backToHome": "Back to Home", "searchJobs": "Search Jobs", "helpCenter": "Help Center"}, "brand": {"name": "Work Finder", "tagline": "Find Your Dream Job"}, "metadata": {"defaultTitle": "Work Finder - Find Your Dream Job", "defaultDescription": "Discover thousands of job opportunities and connect with top employers. Find your perfect career match today.", "authTitle": "Authentication - Work Finder", "authDescription": "Login or create a Work Finder account to access your dashboard and apply for jobs.", "loginTitle": "Login - <PERSON> Finder", "loginDescription": "Login to your Work Finder account to access your dashboard and apply for jobs.", "registerTitle": "Register - Work Finder", "registerDescription": "Create a Work Finder account to start applying for jobs and building your career."}}