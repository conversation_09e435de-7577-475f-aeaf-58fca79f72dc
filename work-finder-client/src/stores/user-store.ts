import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";
import { toast } from "sonner";
import type { User, UserProfile } from "@/types/user";
import {
  loginUser,
  registerUser,
  getCurrentUser,
  logoutUser,
  type LoginRequest,
  type RegisterRequest,
} from "@/lib/api/auth";
import { authTranslations } from "@/lib/i18n/store-translations";

interface AuthStore {
  // State
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;

  // Actions
  login: (data: LoginRequest) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  setUser: (user: User) => void;
  updateProfile: (profile: Partial<UserProfile>) => void;
  clearUser: () => void;

  // Computed
  get userDisplayName(): string;
}

export const useAuthStore = create<AuthStore>()(
  devtools(
    persist(
      immer((set, get) => {
        // Create store instance
        const store = {
          // Initial state
          user: null,
          isAuthenticated: false,
          isLoading: false,

          // Auth Actions
          login: async (data: LoginRequest) => {
            try {
              set((state) => {
                state.isLoading = true;
              });

              const user = await loginUser(data);

              set((state) => {
                state.user = user;
                state.isAuthenticated = true;
                state.isLoading = false;
              });

              // Success toast with translation
              toast.success(authTranslations.loginSuccess());
            } catch (error: unknown) {
              set((state) => {
                state.isLoading = false;
              });
              // Error toast handled globally - no duplicate
              throw error;
            }
          },

          register: async (data: RegisterRequest) => {
            try {
              set((state) => {
                state.isLoading = true;
              });

              const user = await registerUser(data);

              set((state) => {
                state.user = user;
                state.isAuthenticated = true;
                state.isLoading = false;
              });

              // Success toast with translation
              toast.success(authTranslations.registerSuccess());
            } catch (error: unknown) {
              set((state) => {
                state.isLoading = false;
              });
              // Error toast handled in sendRequest - no duplicate
              throw error;
            }
          },

          logout: async () => {
            try {
              await logoutUser();
              set((state) => {
                state.user = null;
                state.isAuthenticated = false;
              });
              // Success toast with translation
              toast.success(authTranslations.logoutSuccess());
            } catch (error) {
              // Even if logout fails on server, clear user locally
              set((state) => {
                state.user = null;
                state.isAuthenticated = false;
              });
              // Error toast handled in sendRequest - no duplicate
            }
          },

          checkAuth: async () => {
            try {
              set((state) => {
                state.isLoading = true;
              });
              const user = await getCurrentUser();

              set((state) => {
                state.user = user;
                state.isAuthenticated = true;
                state.isLoading = false;
              });
            } catch (error) {
              set((state) => {
                state.user = null;
                state.isAuthenticated = false;
                state.isLoading = false;
              });
            }
          },

          // Basic Actions
          setUser: (user: User) =>
            set((state) => {
              state.user = user;
              state.isAuthenticated = true;
            }),

          updateProfile: (profileUpdates: Partial<UserProfile>) =>
            set((state) => {
              if (state.user) {
                state.user.profile = {
                  ...state.user.profile,
                  ...profileUpdates,
                };
              }
            }),

          clearUser: () =>
            set((state) => {
              state.user = null;
              state.isAuthenticated = false;
            }),

          // Computed properties
          get userDisplayName() {
            const user = get().user;
            if (!user) return "";

            const { firstName, lastName } = user.profile;
            if (firstName && lastName) {
              return `${firstName} ${lastName}`;
            }

            return user.name || user.email;
          },
        };

        return store;
      }),
      {
        name: "auth-store",
        partialize: (state: AuthStore) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    ),
    {
      name: "auth-store",
    }
  )
);

export const useUserStore = useAuthStore;
