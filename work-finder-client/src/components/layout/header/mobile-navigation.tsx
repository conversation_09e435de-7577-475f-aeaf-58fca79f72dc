"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import Navigation from "./navigation";
import type { MobileNavigationProps } from "@/types/navigation";

const MobileNavigation = ({
  isOpen,
  onClose,
  items,
}: MobileNavigationProps) => {
  if (!isOpen) return null;

  return (
    <div className="lg:hidden">
      <div
        className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm"
        onClick={onClose}
      />
      <div className="fixed top-0 right-0 z-50 h-full w-80 bg-white shadow-xl">
        <div className="flex items-center justify-between p-4 border-b">
          <span className="text-lg font-semibold">Menu</span>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="p-4">
          <Navigation items={items} isScrolled={true} isMobile={true} />
        </div>
        {/* Mobile Actions */}
        <div className="p-4 border-t space-y-3">
          <Link
            href="/upload-cv"
            className="block px-4 py-2 text-[#1967D2] hover:bg-blue-50 rounded-md transition-colors font-medium"
            onClick={onClose}
          >
            Upload your CV
          </Link>

          <Link
            href="/login"
            className="block px-4 py-2 text-[#1967D2] bg-[#1967D2]/7 hover:bg-[#1967D2]/10 rounded-md transition-colors font-medium"
            onClick={onClose}
          >
            Login / Register
          </Link>

          <Link
            href="/post-job"
            className="block mx-4 py-3 bg-[#1967D2] text-white text-center rounded-md hover:bg-[#1967D2]/90 transition-colors font-medium"
            onClick={onClose}
          >
            Job Post
          </Link>
        </div>
      </div>
    </div>
  );
};

export default MobileNavigation;
