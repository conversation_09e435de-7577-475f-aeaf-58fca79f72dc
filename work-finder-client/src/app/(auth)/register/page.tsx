"use client";

import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PasswordInput } from "@/components/ui/password-input";
import { AUTH_ROUTES } from "@/constants/routes";
import { useTranslation } from "@/hooks/useTranslation";

export default function RegisterPage() {
  const { t } = useTranslation();

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-xl font-bold text-gray-900 mb-1">
          {t("auth.registerTitle")}
        </h1>
        <p className="text-sm text-gray-600">
          {t("metadata.registerDescription")}
        </p>
      </div>

      {/* Registration Form */}
      <form className="space-y-3">
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label
                htmlFor="firstName"
                className="text-xs font-medium text-gray-700"
              >
                {t("auth.firstName")}
              </Label>
              <Input
                id="firstName"
                name="firstName"
                type="text"
                required
                placeholder={t("auth.firstName")}
                className="mt-1 h-10 text-sm border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200 rounded-lg"
              />
            </div>
            <div>
              <Label
                htmlFor="lastName"
                className="text-xs font-medium text-gray-700"
              >
                {t("auth.lastName")}
              </Label>
              <Input
                id="lastName"
                name="lastName"
                type="text"
                required
                placeholder={t("auth.lastName")}
                className="mt-1 h-10 text-sm border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200 rounded-lg"
              />
            </div>
          </div>

          <div>
            <Label
              htmlFor="email"
              className="text-xs font-medium text-gray-700"
            >
              Email
            </Label>
            <Input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              placeholder="<EMAIL>"
              className="mt-1 h-10 text-sm border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200 rounded-lg"
            />
          </div>

          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label
                htmlFor="password"
                className="text-xs font-medium text-gray-700"
              >
                Mật khẩu
              </Label>
              <PasswordInput
                id="password"
                name="password"
                autoComplete="new-password"
                required
                placeholder="Mật khẩu"
                className="mt-1 h-10 text-sm border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200 rounded-lg"
              />
            </div>
            <div>
              <Label
                htmlFor="confirmPassword"
                className="text-xs font-medium text-gray-700"
              >
                Xác nhận
              </Label>
              <PasswordInput
                id="confirmPassword"
                name="confirmPassword"
                autoComplete="new-password"
                required
                placeholder="Xác nhận mật khẩu"
                className="mt-1 h-10 text-sm border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200 rounded-lg"
              />
            </div>
          </div>

          <div>
            <Label className="text-xs font-medium text-gray-700 mb-2 block">
              Loại tài khoản
            </Label>
            <div className="grid grid-cols-2 gap-2">
              <label className="relative cursor-pointer">
                <input
                  type="radio"
                  name="accountType"
                  value="job-seeker"
                  defaultChecked
                  className="sr-only peer"
                />
                <div className="flex items-center justify-center p-2 border border-gray-200 rounded-lg transition-all duration-200 peer-checked:border-blue-500 peer-checked:bg-blue-50 hover:border-gray-300 hover:bg-gray-50">
                  <span className="text-xs font-medium text-gray-700 peer-checked:text-blue-700">
                    Ứng viên
                  </span>
                </div>
              </label>
              <label className="relative cursor-pointer">
                <input
                  type="radio"
                  name="accountType"
                  value="employer"
                  className="sr-only peer"
                />
                <div className="flex items-center justify-center p-2 border border-gray-200 rounded-lg transition-all duration-200 peer-checked:border-blue-500 peer-checked:bg-blue-50 hover:border-gray-300 hover:bg-gray-50">
                  <span className="text-xs font-medium text-gray-700 peer-checked:text-blue-700">
                    Nhà tuyển dụng
                  </span>
                </div>
              </label>
            </div>
          </div>
        </div>

        <div className="flex items-center">
          <input
            id="terms"
            name="terms"
            type="checkbox"
            required
            className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors duration-200"
          />
          <label htmlFor="terms" className="ml-2 block text-xs text-gray-700">
            Tôi đồng ý với{" "}
            <Link
              href="/terms"
              className="text-blue-600 hover:text-blue-700 transition-colors duration-200"
            >
              Điều khoản dịch vụ
            </Link>{" "}
            và{" "}
            <Link
              href="/privacy"
              className="text-blue-600 hover:text-blue-700 transition-colors duration-200"
            >
              Chính sách bảo mật
            </Link>
          </label>
        </div>

        <Button
          type="submit"
          className="w-full h-10 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium text-sm rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
        >
          {t("auth.registerButton")}
        </Button>
      </form>

      {/* Sign in link */}
      <div className="text-center">
        <p className="text-xs text-gray-600">
          {t("auth.alreadyHaveAccount")}{" "}
          <Link
            href={AUTH_ROUTES.LOGIN}
            className="font-medium text-blue-600 hover:text-blue-700 transition-colors duration-200"
          >
            {t("auth.loginButton")}
          </Link>
        </p>
      </div>
    </div>
  );
}
