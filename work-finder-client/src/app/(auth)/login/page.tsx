"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { PasswordInput } from "@/components/ui/password-input";
import { useAuthStore } from "@/stores/user-store";
import { AUTH_ROUTES } from "@/constants/routes";
import { useTranslation } from "@/hooks/useTranslation";

export default function LoginPage() {
  const router = useRouter();
  const { login, isLoading } = useAuthStore();
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    rememberMe: false,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await login({
        email: formData.email,
        password: formData.password,
        rememberMe: formData.rememberMe,
      });

      // Redirect to dashboard after successful login
      router.push("/dashboard");
    } catch (error) {
      // Error is handled by the auth context (toast notification)
      console.error("Login failed:", error);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          {t("auth.loginTitle")}
        </h1>
        <p className="text-gray-600">{t("metadata.loginDescription")}</p>
      </div>

      {/* Login Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-4">
          <div>
            <Label
              htmlFor="email"
              className="text-sm font-medium text-gray-700"
            >
              {t("auth.email")}
            </Label>
            <Input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={formData.email}
              onChange={handleChange}
              placeholder="Nhập email hoặc tên đăng nhập"
              className="mt-1 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200 rounded-lg"
            />
          </div>

          <div>
            <Label
              htmlFor="password"
              className="text-sm font-medium text-gray-700"
            >
              {t("auth.password")}
            </Label>
            <PasswordInput
              id="password"
              name="password"
              autoComplete="current-password"
              required
              value={formData.password}
              onChange={handleChange}
              placeholder={t("auth.password")}
              className="mt-1 h-12 border-gray-200 focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200 rounded-lg"
            />
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <input
              id="remember-me"
              name="rememberMe"
              type="checkbox"
              checked={formData.rememberMe}
              onChange={handleChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors duration-200"
            />
            <label
              htmlFor="remember-me"
              className="ml-2 block text-sm text-gray-700"
            >
              {t("auth.rememberMe")}
            </label>
          </div>

          <div className="text-sm">
            <Link
              href={AUTH_ROUTES.FORGOT_PASSWORD}
              className="font-medium text-blue-600 hover:text-blue-700 transition-colors duration-200"
            >
              {t("auth.forgotPassword")}
            </Link>
          </div>
        </div>

        <Button
          type="submit"
          disabled={isLoading}
          className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-medium rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl disabled:opacity-50"
        >
          {isLoading ? t("common.loading") : t("auth.loginButton")}
        </Button>
      </form>

      {/* Sign up link */}
      <div className="text-center">
        <p className="text-sm text-gray-600">
          {t("auth.dontHaveAccount")}{" "}
          <Link
            href={AUTH_ROUTES.REGISTER}
            className="font-medium text-blue-600 hover:text-blue-700 transition-colors duration-200"
          >
            {t("auth.registerButton")}
          </Link>
        </p>
      </div>
    </div>
  );
}
