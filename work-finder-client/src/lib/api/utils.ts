/**
 * API Utility Functions
 * Shared utilities for API functions
 */

/**
 * Helper function to handle API responses
 * Provides consistent error handling across all API functions
 */
export async function handleResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

    try {
      const errorData = await response.json();
      if (errorData.message) {
        errorMessage = errorData.message;
      } else if (errorData.result?.messages?.[0]?.message) {
        errorMessage = errorData.result.messages[0].message;
      }
    } catch {
      // If JSON parsing fails, use default error message
    }

    const error = new Error(errorMessage) as Error & { status: number };
    error.status = response.status;
    throw error;
  }

  return response.json();
}

/**
 * Build query string from parameters
 * Handles arrays and filters out empty values
 */
export function buildQueryString(params: Record<string, unknown>): string {
  const searchParams = new URLSearchParams();

  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      if (Array.isArray(value)) {
        value.forEach((item) => searchParams.append(key, item.toString()));
      } else {
        searchParams.append(key, value.toString());
      }
    }
  });

  return searchParams.toString();
}

/**
 * Get base API URL
 */
export function getApiBaseUrl(): string {
  return process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000/api/v1";
}

/**
 * Create fetch options with default settings
 */
export function createFetchOptions(options: RequestInit = {}): RequestInit {
  return {
    credentials: "include", // Always include HttpOnly cookies
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
    ...options,
  };
}
