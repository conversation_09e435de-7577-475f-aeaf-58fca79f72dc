export interface ApiResponse<T = unknown> {
  data: T;
  message: string;
  status: "success" | "error";
  timestamp: string;
}

export interface PaginatedResponse<T = unknown> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message: string;
  status: "success" | "error";
  timestamp: string;
}

// Common paginated response format used by API endpoints
export interface PaginatedApiResponse<T = unknown> {
  items: T[];
  total: number;
  page: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

export interface ApiError {
  message: string;
  statusCode: number;
  error: string;
  timestamp: string;
}

export type QueryKey = readonly unknown[];

export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

// Common search parameters interface
export interface BaseSearchParams extends Record<string, unknown> {
  q?: string;
  page?: number;
  limit?: number;
  sort?: string;
}
