// HTTP Methods
const METHOD = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH'
} as const;

// API Endpoints Configuration (Following @vsii-internal-fe pattern)
const API_ENDPOINTS = {
  auth: {
    login: {
      method: METHOD.POST,
      url: 'auth/login',
      dontNeedToken: true,
      isAuth: true
    },
    register: {
      method: METHOD.POST,
      url: 'auth/register', 
      dontNeedToken: true,
      isAuth: true
    },
    logout: {
      method: METHOD.POST,
      url: 'auth/logout',
      dontNeedToken: false,
      isAuth: true
    },
    refresh: {
      method: METHOD.POST,
      url: 'auth/refresh',
      dontNeedToken: true,
      isAuth: true
    },
    me: {
      method: METHOD.GET,
      url: 'auth/me',
      dontNeedToken: false,
      isAuth: true
    },
    forgotPassword: {
      method: METHOD.POST,
      url: 'auth/forgot-password',
      dontNeedToken: true,
      isAuth: true
    },
    resetPassword: {
      method: METHOD.POST,
      url: 'auth/reset-password',
      dontNeedToken: true,
      isAuth: true
    },
    changePassword: {
      method: METHOD.PUT,
      url: 'auth/change-password',
      dontNeedToken: false,
      isAuth: true
    }
  },
  jobs: {
    getAll: {
      method: METHOD.GET,
      url: 'jobs',
      dontNeedToken: false
    },
    getById: (id: string) => ({
      method: METHOD.GET,
      url: `jobs/${id}`,
      dontNeedToken: false
    }),
    create: {
      method: METHOD.POST,
      url: 'jobs',
      dontNeedToken: false
    },
    update: (id: string) => ({
      method: METHOD.PUT,
      url: `jobs/${id}`,
      dontNeedToken: false
    }),
    delete: (id: string) => ({
      method: METHOD.DELETE,
      url: `jobs/${id}`,
      dontNeedToken: false
    }),
    search: {
      method: METHOD.GET,
      url: 'jobs/search',
      dontNeedToken: false
    },
    apply: (id: string) => ({
      method: METHOD.POST,
      url: `jobs/${id}/apply`,
      dontNeedToken: false
    }),
    saveJob: (id: string) => ({
      method: METHOD.POST,
      url: `jobs/${id}/save`,
      dontNeedToken: false
    }),
    unsaveJob: (id: string) => ({
      method: METHOD.DELETE,
      url: `jobs/${id}/save`,
      dontNeedToken: false
    })
  },
  companies: {
    getAll: {
      method: METHOD.GET,
      url: 'companies',
      dontNeedToken: false
    },
    getById: (id: string) => ({
      method: METHOD.GET,
      url: `companies/${id}`,
      dontNeedToken: false
    }),
    create: {
      method: METHOD.POST,
      url: 'companies',
      dontNeedToken: false
    },
    update: (id: string) => ({
      method: METHOD.PUT,
      url: `companies/${id}`,
      dontNeedToken: false
    }),
    delete: (id: string) => ({
      method: METHOD.DELETE,
      url: `companies/${id}`,
      dontNeedToken: false
    }),
    search: {
      method: METHOD.GET,
      url: 'companies/search',
      dontNeedToken: false
    },
    getJobs: (id: string) => ({
      method: METHOD.GET,
      url: `companies/${id}/jobs`,
      dontNeedToken: false
    }),
    follow: (id: string) => ({
      method: METHOD.POST,
      url: `companies/${id}/follow`,
      dontNeedToken: false
    }),
    unfollow: (id: string) => ({
      method: METHOD.DELETE,
      url: `companies/${id}/follow`,
      dontNeedToken: false
    })
  },
  applications: {
    getAll: {
      method: METHOD.GET,
      url: 'applications',
      dontNeedToken: false
    },
    getById: (id: string) => ({
      method: METHOD.GET,
      url: `applications/${id}`,
      dontNeedToken: false
    }),
    create: {
      method: METHOD.POST,
      url: 'applications',
      dontNeedToken: false
    },
    update: (id: string) => ({
      method: METHOD.PUT,
      url: `applications/${id}`,
      dontNeedToken: false
    }),
    withdraw: (id: string) => ({
      method: METHOD.DELETE,
      url: `applications/${id}`,
      dontNeedToken: false
    }),
    getStats: {
      method: METHOD.GET,
      url: 'applications/stats',
      dontNeedToken: false
    }
  },
  profile: {
    get: {
      method: METHOD.GET,
      url: 'profile',
      dontNeedToken: false
    },
    update: {
      method: METHOD.PUT,
      url: 'profile',
      dontNeedToken: false
    },
    uploadAvatar: {
      method: METHOD.POST,
      url: 'profile/avatar',
      dontNeedToken: false,
      upload: true
    },
    uploadResume: {
      method: METHOD.POST,
      url: 'profile/resume',
      dontNeedToken: false,
      upload: true
    }
  }
} as const;

export default API_ENDPOINTS;
